'use client'

import { StarIcon } from '@ttplatform/core-page-builder/components'
import { Typography } from '@ttplatform/ui/components'
import { motion } from 'framer-motion'
import React, { useCallback, useState } from 'react'
import {
  RATING_CONFIG,
  RATING_LABELS,
  RATING_SCALE,
} from '../../constants/survey.constants'
import { useSurveyData } from '../../hooks/use-survey-data'
import { useSurveyValidation } from '../../hooks/use-survey-validation'
import {
  RatingLevel,
  RatingQuestion as RatingQuestionType,
} from '../../types/survey.types'

interface RatingQuestionProps {
  question: RatingQuestionType
  value: number
  onChange: (value: number) => void
  error?: string
}

export default function RatingQuestion({
  question,
  value = 0,
  onChange,
  error,
}: RatingQuestionProps) {
  const [isDragging, setIsDragging] = useState(false)
  const [hoverValue, setHoverValue] = useState<number | null>(null)
  // const [dragStartX, setDragStartX] = useState(0)
  const [sliderRect, setSliderRect] = useState<DOMRect | null>(null)
  const { shouldShowFollowUp } = useSurveyData()
  const { clearQuestionError } = useSurveyValidation()

  const minValue = question.minValue || RATING_SCALE.min
  const maxValue = question.maxValue || RATING_SCALE.max
  const showFollowUp = shouldShowFollowUp(question.id)

  const getRatingLevel = (rating: number): RatingLevel => {
    if (rating >= RATING_CONFIG.thresholds.satisfied) return 'satisfied'
    if (rating >= RATING_CONFIG.thresholds.neutral) return 'neutral'
    return 'dissatisfied'
  }

  const getRatingColor = (rating: number): string => {
    const level = getRatingLevel(rating)
    return RATING_CONFIG.colors[level]
  }

  const getRatingLabel = (rating: number): string => {
    const level = getRatingLevel(rating)
    return RATING_LABELS[level]
  }

  const currentRating = hoverValue || value
  const currentColor =
    currentRating > 0 ? getRatingColor(currentRating) : '#D1D5DB'
  const currentLabel =
    currentRating > 0 ? getRatingLabel(currentRating) : 'Chưa đánh giá'

  const handleSliderChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = Number(e.target.value)
    onChange(newValue)
  }

  const handleMouseEnter = (rating: number) => {
    if (!isDragging) {
      setHoverValue(rating)
    }
  }

  const handleMouseLeave = () => {
    if (!isDragging) {
      setHoverValue(null)
    }
  }

  const handleClick = (rating: number) => {
    // Clear error immediately when user interacts
    if (error) {
      clearQuestionError(question.id)
    }
    onChange(rating)
  }

  const handleStarMouseDown = (e: React.MouseEvent) => {
    e.preventDefault()
    setIsDragging(true)
    const sliderElement = e.currentTarget.closest(
      '.rating-slider',
    ) as HTMLElement
    if (sliderElement) {
      setSliderRect(sliderElement.getBoundingClientRect())
    }
  }

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isDragging || !sliderRect) return

      requestAnimationFrame(() => {
        const x = e.clientX - sliderRect.left
        const percentage = Math.max(0, Math.min(1, x / sliderRect.width))
        const newValue = Math.round(
          minValue + percentage * (maxValue - minValue),
        )

        if (
          newValue >= minValue &&
          newValue <= maxValue &&
          newValue !== value
        ) {
          onChange(newValue)
        }
      })
    },
    [isDragging, sliderRect, minValue, maxValue, value, onChange],
  )

  const handleMouseUp = useCallback(() => {
    setIsDragging(false)
    setSliderRect(null)
  }, [])

  // Add global mouse events for dragging
  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
      }
    }
  }, [isDragging])

  // Generate scale numbers
  const scaleNumbers = Array.from(
    { length: maxValue - minValue + 1 },
    (_, i) => minValue + i,
  )

  return (
    <motion.div
      className="space-y-6 mt-20"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      {/* Rating Slider */}
      <motion.div
        className="relative rating-slider"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        {/* Hidden Input Slider */}
        <input
          type="range"
          min={minValue}
          max={maxValue}
          value={value}
          onChange={handleSliderChange}
          onMouseDown={() => setIsDragging(true)}
          onMouseUp={() => setIsDragging(false)}
          className="slider absolute top-0 left-0 w-full h-1 opacity-0 cursor-pointer z-10"
        />

        {/* Gradient Background */}
        <div className="h-[6px] rounded-full bg-gradient-to-r from-[#D92D20] via-[#EEDE77] to-[#079455]"></div>

        <div className="mt-3 flex justify-between select-none">
          {scaleNumbers.map((num) => (
            <div
              key={num}
              className="flex flex-col items-center space-y-2 relative"
            >
              <div className="h-16 flex flex-col items-center justify-end absolute -top-[68px]">
                {currentRating === num && (
                  <motion.div
                    className="flex flex-col items-center space-y-1"
                    initial={{ opacity: 0, scale: 0 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.2 }}
                  >
                    {/* Rating Label */}
                    <div
                      className="px-3 py-1 rounded-lg text-white text-sm font-medium mb-2 relative h-10 flex items-center justify-center whitespace-nowrap"
                      style={{ backgroundColor: currentColor }}
                    >
                      {currentLabel}
                      {/* Arrow pointing down */}
                      <div
                        className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent"
                        style={{ borderTopColor: currentColor }}
                      ></div>
                    </div>

                    {/* Star Icon */}
                    <motion.div
                      className="cursor-grab active:cursor-grabbing select-none"
                      style={{ color: currentColor }}
                      animate={{ scale: isDragging ? 1.2 : 1 }}
                      transition={{ duration: 0.1 }}
                      onMouseDown={handleStarMouseDown}
                    >
                      <StarIcon className="w-7 h-7" />
                    </motion.div>
                  </motion.div>
                )}
              </div>

              {/* Number button */}
              <button
                onClick={() => handleClick(num)}
                onMouseEnter={() => handleMouseEnter(num)}
                onMouseLeave={handleMouseLeave}
                className="cursor-pointer"
              >
                <Typography variant="body2">{num}</Typography>

                {/* Tooltip - chỉ hiện khi không có rating */}
                {hoverValue === num && value === 0 && (
                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-popover border border-border text-popover-foreground text-xs rounded whitespace-nowrap z-20 shadow-md">
                    <Typography variant="caption">
                      {getRatingLabel(num)}
                    </Typography>
                    <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-border"></div>
                  </div>
                )}
              </button>
            </div>
          ))}
        </div>
      </motion.div>

      {/* Custom Styles for Slider */}
      <style jsx>{`
        .slider::-webkit-slider-thumb {
          appearance: none;
          width: 20px;
          height: 20px;
          border-radius: 50%;
          background: ${currentColor};
          border: 2px solid white;
          box-shadow: 0 2px 4px rgba(0,0,0,0.2);
          cursor: pointer;
          transition: all 0.1s ease;
        }
        
        .slider::-webkit-slider-thumb:hover {
          transform: scale(1.1);
          box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }
        
        .slider::-moz-range-thumb {
          width: 20px;
          height: 20px;
          border-radius: 50%;
          background: ${currentColor};
          border: 2px solid white;
          box-shadow: 0 2px 4px rgba(0,0,0,0.2);
          cursor: pointer;
          transition: all 0.1s ease;
        }
        
        .slider::-moz-range-thumb:hover {
          transform: scale(1.1);
          box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }
      `}</style>
    </motion.div>
  )
}
