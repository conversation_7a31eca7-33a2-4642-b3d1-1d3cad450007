'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { MainButton } from '@ttplatform/core-page-builder/components'
import { Input, Label, Textarea, Typography } from '@ttplatform/ui/components'
import { ArrowLeft } from 'lucide-react'
import { useForm } from 'react-hook-form'
import { useSurveyContext } from '../context/survey-context'
import { useSurveyValidation } from '../hooks'
import { useSurveyData } from '../hooks/use-survey-data'
import { useSurveyNavigation } from '../hooks/use-survey-navigation'
import { type ConfirmationFormData, surveySchemas } from '../schemas/validation-schemas'
import BaseStep from './base-step'

interface SurveyConfirmationStepProps {
  stepConfig?: any
}

export default function SurveyConfirmationStep({
  stepConfig,
}: SurveyConfirmationStepProps) {
  const { getCurrentStep } = useSurveyContext()
  const { getAnswer, setAnswer } = useSurveyData()
  const { getQuestionError, clearQuestionError } = useSurveyValidation()
  const { handleButtonAction, canGoNext, canGoPrevious } = useSurveyNavigation()

  const step = stepConfig || getCurrentStep()

  // Initialize React Hook Form
  const form = useForm<ConfirmationFormData>({
    resolver: zodResolver(surveySchemas.confirmation),
    mode: 'onSubmit',
    reValidateMode: 'onChange',
    defaultValues: {
      contact_name: getAnswer('contact_name') || '',
      contact_phone: getAnswer('contact_phone') || '',
      contact_email: getAnswer('contact_email') || '',
      company_name: getAnswer('company_name') || '',
      company_address: getAnswer('company_address') || '',
      company_tax_code: getAnswer('company_tax_code') || '',
    },
  })

  const { register, handleSubmit, formState: { errors, isValid, isSubmitting }, trigger, setValue, getValues } = form

  // Debug form state
  console.log('Form state:', { errors, isValid, isSubmitting, canGoNext })
  console.log('Form values:', getValues())

  // Form submission handler
  const onSubmit = async (data: ConfirmationFormData) => {
    try {
      // Update all answers in survey context
      Object.entries(data).forEach(([key, value]) => {
        setAnswer(key, value)
      })

      // Clear any existing errors for this step
      const currentStep = getCurrentStep()
      if (currentStep) {
        // Clear errors for this step in survey context
        Object.keys(data).forEach(fieldId => {
          clearQuestionError(fieldId)
        })
      }

      // Debug logging
      console.log('Form submitted successfully:', data)
      console.log('canGoNext:', canGoNext)
      console.log('Will call handleButtonAction with:', canGoNext ? 'next' : 'submit')

      // Proceed to next step
      handleButtonAction(canGoNext ? 'next' : 'submit')
    } catch (error) {
      console.error('Form submission error:', error)
    }
  }

  const onError = (formErrors: any) => {
    console.log('Form validation errors:', formErrors)

    // Trigger validation for all fields to show errors
    Object.keys(formErrors).forEach((field) => {
      trigger(field as keyof ConfirmationFormData)
    })

    // Scroll to first error field
    const firstErrorField = Object.keys(formErrors)[0]
    if (firstErrorField) {
      const errorElement = document.getElementById(firstErrorField)
      if (errorElement) {
        errorElement.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        })

        // Add shake animation
        errorElement.style.animation = 'shake 0.5s ease-in-out'
        setTimeout(() => {
          errorElement.style.animation = ''
        }, 500)
      }
    }
  }

  if (!step) {
    return null
  }

  // Convert questions to form fields format
  const fields =
    step.questions?.map((question: any) => ({
      id: question.id,
      title: question.title,
      type: question.type === 'open-ended' ? 'text' : question.type,
      required: question.required,
      placeholder: (question as any).placeholder,
      maxLength: (question as any).maxLength,
      minLength: (question as any).minLength,
    })) || []

  // Parse content configuration
  const content = step.content ? JSON.parse(step.content) : null

  const handleInputChange = (fieldId: string, value: string) => {
    // Update React Hook Form
    setValue(fieldId as keyof ConfirmationFormData, value, { shouldValidate: true })

    // Clear error immediately when user starts interacting
    const error = getQuestionError(fieldId)
    if (error) {
      clearQuestionError(fieldId)
    }

    // Update survey context
    setAnswer(fieldId, value)
  }

  const renderField = (field: any) => {
    // Prioritize React Hook Form errors over survey context errors
    const rhfError = errors[field.id as keyof ConfirmationFormData]?.message
    const surveyError = getQuestionError(field.id)
    const error = rhfError || surveyError

    return (
      <div key={field.id}>
        <Label htmlFor={field.id} className="mb-2 text-gray-600">
          <Typography variant="caption">
            {field.title}
            {field.required && <span className="text-destructive ml-1">*</span>}
          </Typography>
        </Label>

        {field.type === 'textarea' ? (
          <Textarea
            {...register(field.id as keyof ConfirmationFormData)}
            id={field.id}
            onChange={(e) => handleInputChange(field.id, e.target.value)}
            className={`bg-white min-h[100px] focus-visible:ring-primary resize-vertical p-3 border border-gray-200 ${error ? 'border-destructive focus-visible:ring-destructive' : ''}`}
            placeholder={field.placeholder}
            rows={field.rows || 3}
            maxLength={field.maxLength}
            minLength={field.minLength}
          />
        ) : (
          <Input
            {...register(field.id as keyof ConfirmationFormData)}
            id={field.id}
            type={field.type || 'text'}
            onChange={(e) => handleInputChange(field.id, e.target.value)}
            className={`bg-white border-gray-200 text-gray-800 ${error ? 'border-destructive focus-visible:ring-destructive h-12' : ''}`}
            placeholder={field.placeholder}
            maxLength={field.maxLength}
            minLength={field.minLength}
          />
        )}

        {error && (
          <Typography
            variant="caption"
            className="text-destructive !font-normal block mt-1"
          >
            {error}
          </Typography>
        )}
      </div>
    )
  }

  const renderStepButtons = (
    <>
      {canGoPrevious && (
        <MainButton
          label="QUAY LẠI"
          variant="primary"
          iconPosition="left"
          CustomIcon={ArrowLeft}
          onClick={() => handleButtonAction('previous')}
        />
      )}

      <MainButton
        label={canGoNext ? 'TIẾP TỤC' : 'GỬI'}
        variant="secondary"
        onClick={async () => {
          console.log('Submit button clicked')
          console.log('Current form errors:', errors)
          console.log('Form is valid:', Object.keys(errors).length === 0)

          // Trigger validation first
          const isFormValid = await trigger()
          console.log('Trigger validation result:', isFormValid)

          if (isFormValid) {
            console.log('Form is valid, calling handleSubmit')
            handleSubmit(onSubmit, onError)()
          } else {
            console.log('Form has errors, calling onError')
            onError(errors)
          }
        }}
      />
    </>
  )

  return (
    <>
      <style jsx>{`
        @keyframes shake {
          0%, 100% { transform: translateX(0); }
          10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
          20%, 40%, 60%, 80% { transform: translateX(5px); }
        }
      `}</style>

      <BaseStep step={step}>
        {/* Dynamic Info text */}
        {content?.infoText && (
          <div>
            {content.infoText.description.map((text: string, index: number) => (
              <Typography key={index} variant="body1">
                {text}
              </Typography>
            ))}
          </div>
        )}

        {/* Line */}
        <div className="h-px w-full bg-gray-200 mx-auto"></div>

        <form
          onSubmit={handleSubmit(onSubmit, onError)}
          className="space-y-10"
          noValidate
          onSubmitCapture={(e) => {
            console.log('Form submit captured:', e)
          }}
        >
          {content?.sections?.map((section: any) => (
            <section key={section.id} className="space-y-6">
              <Typography variant="h4">{section.title}</Typography>

              {section.type === 'form' && (
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                  {fields.map(renderField)}
                </div>
              )}

              {section.type === 'readonly' && (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 mb-4">
                    {section.fields
                      ?.filter((field: any) => field.type !== 'textarea')
                      .map((field: any) => (
                        <div key={field.id}>
                          <Label
                            htmlFor={field.id}
                            className="mb-2 text-gray-600"
                          >
                            <Typography
                              variant="caption"
                              className="text-foreground"
                            >
                              {field.label}
                            </Typography>
                          </Label>
                          <Input
                            id={field.id}
                            type="text"
                            defaultValue={field.value}
                            readOnly
                            className="bg-[#eaecf0] text-gray-700 cursor-not-allowed"
                          />
                        </div>
                      ))}
                  </div>

                  {section.fields
                    ?.filter((field: any) => field.type === 'textarea')
                    .map((field: any) => (
                      <div key={field.id}>
                        <Label htmlFor={field.id} className="mb-2 text-gray-600">
                          <Typography
                            variant="caption"
                            className="text-foreground"
                          >
                            {field.label}
                          </Typography>
                        </Label>
                        <Textarea
                          id={field.id}
                          rows={3}
                          defaultValue={field.value}
                          readOnly
                          className="bg-[#eaecf0] text-gray-700 min-h-[100px] p-3 cursor-not-allowed resize-none"
                        />
                      </div>
                    ))}
                </div>
              )}
            </section>
          )) || (
              // Fallback to default form if no sections configured
              <section className="space-y-6">
                <Typography variant="h4">
                  THÔNG TIN CÔNG TY VÀ NGƯỜI PHỤ TRÁCH
                </Typography>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                  {fields.map(renderField)}
                </div>
              </section>
            )}
        </form>

        {/* Render Button */}
        {renderStepButtons && (
          <div className="flex justify-center gap-x-4 xl:gap-x-10">{renderStepButtons}</div>
        )}
      </BaseStep>
    </>
  )
}
